# ================================
# Stage 1: Build frontend
# ================================
FROM node:18-alpine AS web-builder

# 设置npm国内镜像源，提高下载稳定性
# 通过环境变量设置二进制文件镜像，避免npm config的兼容性问题
ENV ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ \
    SASS_BINARY_SITE=https://npmmirror.com/mirrors/node-sass/ \
    PHANTOMJS_CDNURL=https://npmmirror.com/mirrors/phantomjs/ \
    CHROMEDRIVER_CDNURL=https://npmmirror.com/mirrors/chromedriver/ \
    OPERADRIVER_CDNURL=https://npmmirror.com/mirrors/operadriver/ \
    PYTHON_MIRROR=https://npmmirror.com/mirrors/python/

# 配置npm镜像源
RUN npm config set registry https://registry.npmmirror.com && \
    npm config set cache /root/.npm

# 安装pnpm并设置镜像源
RUN npm install -g pnpm@latest --verbose && \
    pnpm config set registry https://registry.npmmirror.com && \
    pnpm config set store-dir /root/.pnpm-store && \
    pnpm config set fetch-retries 2

WORKDIR /app/web

# 先复制依赖文件，利用Docker层缓存
COPY web/package.json web/pnpm-lock.yaml* ./

# 安装依赖，使用缓存挂载提高构建速度
RUN --mount=type=cache,target=/root/.pnpm-store \
    --mount=type=cache,target=/root/.npm \
    pnpm install --frozen-lockfile --ignore-scripts --loglevel  verbose && \
    pnpm store prune

# 复制源代码并构建
COPY web/ ./
RUN pnpm run build && \
    # 清理不必要的node_modules减少传输到下一阶段的数据
    rm -rf node_modules

# ================================
# Stage 2: Python dependencies builder
# ================================
FROM python:3.12-alpine AS python-builder

# 配置pip国内镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn && \
    pip config set global.timeout 120

# 安装编译依赖
RUN --mount=type=cache,target=/var/cache/apk \
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories && \
    apk update && \
    apk add --no-cache \
        build-base \
        cmake \
        ninja-build \
        ffmpeg-dev \
        libjpeg-turbo-dev \
        libpng-dev \
        tiff-dev \
        libwebp-dev \
        openblas-dev \
        eigen-dev \
        onetbb-dev \
        cargo \
        rust \
        libffi-dev \
        openssl-dev

# 安装uv用于更快的Python包管理
RUN pip install -U uv --verbose && \
    uv --version

WORKDIR /app

# 复制Python依赖文件
COPY pyproject.toml uv.lock ./

# 创建虚拟环境并安装依赖
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=cache,target=/root/.cache/pip \
    # 注意：. /opt/venv/bin/activate 只在当前RUN指令的shell中生效
    # 后续命令需要直接调用 /opt/venv/bin/uv 或 /opt/venv/bin/pip
    uv pip sync --system --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
                   --trusted-host pypi.tuna.tsinghua.edu.cn \
                   --compile-bytecode \
                   --verbose \
                   uv.lock 
                   #pyproject.toml 
    # /opt/venv/bin/uv pip list # 如果需要验证，可以保留，否则移除

# ================================
# Stage 3: Runtime image
# ================================
FROM python:3.12-alpine AS production

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# 创建非root用户
RUN addgroup -S appuser && adduser -S appuser -G appuser

# 安装运行时依赖，设置时区，并清理
RUN --mount=type=cache,target=/var/cache/apk \
    apk add --no-cache \
        nginx \
        curl \
        tini \
        tzdata \
        libjpeg-turbo \
        libpng \
        tiff \
        libwebp \
        libwebp-tools \
        openblas \
        ffmpeg \
        supervisor \
        && \
    # 设置时区
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    # 清理不必要的文件
    rm -rf /var/cache/apk/* \
           /tmp/* \
           /var/tmp/* \
           /usr/share/doc \
           /usr/share/man

WORKDIR /app

# 从构建阶段复制Python虚拟环境
#COPY --from=python-builder /opt/venv /opt/venv
COPY --from=python-builder /usr/local/lib/python3.12/site-packages/ /usr/local/lib/python3.12/site-packages/
COPY --from=python-builder /usr/local/bin/ /usr/local/bin/

# # 确保使用虚拟环境
# ENV PATH="/opt/venv/bin:$PATH"

# 验证Python环境 (可选，如果不需要在构建日志中看到，可以移除)
# RUN python --version && pip list

# 复制应用代码（按变更频率排序，利用缓存）
COPY --chown=appuser:appuser pyproject.toml ./
COPY --chown=appuser:appuser migrations/ ./migrations/
COPY --chown=appuser:appuser app/ ./app/
COPY --chown=appuser:appuser *.py ./

# 从前端构建阶段复制静态文件
COPY --from=web-builder --chown=appuser:appuser /app/web/dist ./web/dist

# 配置nginx并创建相关目录
RUN rm -f /etc/nginx/http.d/default.conf && \
    mkdir -p /var/log/nginx /var/lib/nginx /run/nginx && \
    mkdir -p /var/log/supervisor /var/run/supervisor && \
    chown -R appuser:appuser /var/log/nginx /var/lib/nginx /run/nginx /etc/nginx /var/log/supervisor /var/run/supervisor
COPY deploy/web.conf /etc/nginx/http.d/web.conf

# 创建应用所需目录并设置权限
RUN chown -R appuser:appuser /app

# 设置环境变量
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app
    # PATH 已经在前面设置过了，这里不需要重复

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/api/health || exit 1

# 复制supervisor配置
COPY deploy/supervisord.conf /etc/supervisord.conf
RUN chown appuser:appuser /etc/supervisord.conf

# 暴露端口
EXPOSE 80

# 使用tini作为init系统，启动supervisor
ENTRYPOINT ["tini", "--", "supervisord", "-c", "/etc/supervisord.conf"]