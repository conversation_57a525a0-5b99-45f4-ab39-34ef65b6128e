# app/services/minio_client.py (or similar)

from typing import Optional, Any, Dict, List, Union
from minio import Minio
from minio.error import S3Error
from io import Bytes<PERSON>
from datetime import timedelta

from app.settings.config import settings
from app.utils import singleton
from app.log import logger


@singleton
class MinioClient:
    """
    MinioClient for object storage management.
    This class provides methods to interact with Minio object storage.
    """
    def __init__(self) -> None:
        self.conn: Optional[Minio] = None
        self.__open__()

    def __open__(self) -> None:
        """
        Initialize or reinitialize the Minio connection.
        """
        try:
            if self.conn:
                self.__close__()
        except Exception as e:
            logger.error(f"Error closing Minio connection: {e}")
            raise

        try:
            self.conn = Minio(settings.MINIO["host"],
                              access_key=settings.MINIO["user"],
                              secret_key=settings.MINIO["password"],
                              secure=settings.MINIO.get("secure", False)
                              )
        except Exception as e:
            logger.error(f"Failed to connect to Minio server at {settings.MINIO['host']}: {e}")
            raise

    def __close__(self) -> None:
        """
        Close the Minio connection.
        """
        del self.conn
        self.conn = None

    def health(self) -> Any:
        """
        Check if the Minio connection is healthy.

        Returns:
            Any: Result of the health check operation
        """
        try:
            bucket, fnm, binary = "health-check", "health-check", b"health-check"
            if not self.conn.bucket_exists(bucket):
                self.conn.make_bucket(bucket)
            r = self.conn.put_object(bucket, fnm,
                                    BytesIO(binary),
                                    len(binary)
                                    )
            return r
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            raise

    def put(self, bucket: str, fnm: str, binary: bytes) -> Any:
        """
        Upload an object to Minio.

        Args:
            bucket: Bucket name
            fnm: Object name
            binary: Object data

        Returns:
            Any: Result of the put operation
        """
        try:
            if not self.conn.bucket_exists(bucket):
                self.conn.make_bucket(bucket)

            r = self.conn.put_object(bucket, fnm,
                                    BytesIO(binary),
                                    len(binary)
                                    )
            return r
        except Exception as e:
            logger.error(f"Failed to put object {bucket}/{fnm}: {e}")
            raise

    def rm(self, bucket: str, fnm: str) -> None:
        """
        Remove an object from Minio.

        Args:
            bucket: Bucket name
            fnm: Object name
        """
        try:
            self.conn.remove_object(bucket, fnm)
        except Exception as e:
            logger.error(f"Failed to remove object {bucket}/{fnm}: {e}")
            raise

    def get(self, bucket: str, fnm: str) -> bytes:
        """
        Get an object from Minio.

        Args:
            bucket: Bucket name
            fnm: Object name

        Returns:
            bytes: Object data
        """
        try:
            r = self.conn.get_object(bucket, fnm)
            return r.read()
        except Exception as e:
            logger.error(f"Failed to get object {bucket}/{fnm}: {e}")
            raise

    def obj_exist(self, bucket: str, fnm: str) -> bool:
        """
        Check if an object exists in Minio.

        Args:
            bucket: Bucket name
            fnm: Object name

        Returns:
            bool: True if object exists, False otherwise
        """
        try:
            if not self.conn.bucket_exists(bucket):
                return False
            self.conn.stat_object(bucket, fnm)
            return True
        except S3Error as e:
            if e.code in ["NoSuchKey", "NoSuchBucket", "ResourceNotFound"]:
                return False
            logger.error(f"S3Error checking if object {bucket}/{fnm} exists: {e}")
            raise
        except Exception as e:
            logger.error(f"Error checking if object {bucket}/{fnm} exists: {e}")
            raise

    def get_presigned_url(self, bucket: str, fnm: str, expires: Union[int, timedelta]) -> str:
        """
        Get a presigned URL for an object.

        Args:
            bucket: Bucket name
            fnm: Object name
            expires: Expiration time in seconds (int) or as timedelta object

        Returns:
            str: Presigned URL
        """
        try:
            # Convert expires to timedelta if it's an integer
            if isinstance(expires, int):
                expires_delta = timedelta(seconds=expires)
            else:
                expires_delta = expires

            return self.conn.get_presigned_url("GET", bucket, fnm, expires_delta)
        except Exception as e:
            logger.error(f"Failed to get presigned URL for {bucket}/{fnm}: {e}")
            raise

    def list_objects(self, bucket: str, prefix: str = "", recursive: bool = True) -> List[Dict[str, Any]]:
        """
        List objects in a bucket.

        Args:
            bucket: Bucket name
            prefix: Prefix to filter objects
            recursive: If True, recursively list objects

        Returns:
            List[Dict[str, Any]]: List of objects
        """
        try:
            if not self.conn.bucket_exists(bucket):
                return []

            objects = []
            for obj in self.conn.list_objects(bucket, prefix=prefix, recursive=recursive):
                objects.append({
                    "object_name": obj.object_name,
                    "size": obj.size,
                    "last_modified": obj.last_modified,
                    "etag": obj.etag
                })
            return objects
        except Exception as e:
            logger.error(f"Failed to list objects in bucket {bucket} with prefix {prefix}: {e}")
            raise


# Initialize the MinioClient singleton
minioClient = MinioClient()