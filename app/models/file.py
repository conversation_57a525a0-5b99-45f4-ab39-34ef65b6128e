from tortoise import fields, models

from .base import BaseModel, TimestampMixin
from .enums import HashAlgorithm, ProcessingStatus
# from .admin import User # Ensure this is available or use "models.User"

# --- Model Definition ---
class File(BaseModel, TimestampMixin):
    """
    Model to store metadata for files and directories stored in object storage (MinIO).
    Represents both files and directories.
    """
    # 1. Delete user will not delete the files:
    #    - User field should be nullable if user can be deleted.
    #    - on_delete=fields.SET_NULL will set user_id to NULL when user is deleted.
    user = fields.ForeignKeyField(
        "models.User",
        related_name="files",
        description="关联用户 (文件所有者)",
        index=True,
        null=True,  # Allow user_id to be NULL if user is deleted
        on_delete=fields.SET_NULL # If user is deleted, set user_id to NULL on these files
    )

    # Self-referencing foreign key for directory structure
    # 1. Delete folder (parent) will delete all files under it:
    #    - on_delete=fields.CASCADE will delete children if parent is deleted.
    #    - This applies to hard deletes. For soft deletes, app logic handles recursion.
    parent: fields.ForeignKeyNullableRelation["File"] = fields.ForeignKeyField(
        "models.File",
        related_name="children",
        null=True,               # Root items have no parent
        description="父级ID (指向父目录)",
        index=True,
        on_delete=fields.CASCADE # If a parent directory is (hard) deleted, cascade delete children
    )

    # --- Core Attributes ---
    is_directory = fields.BooleanField(default=False, description="是否为目录", index=True)
    name = fields.CharField(max_length=255, description="当前名称 (文件或目录名)", index=True)
    original_name = fields.CharField(max_length=255, null=True, description="原始文件名 (上传时)")
    description = fields.TextField(null=True, description="文件或目录描述")

    # --- File Specific Attributes (Nullable for directories) ---
    mime_type = fields.CharField(max_length=100, null=True, description="MIME类型", index=True)
    file_extension = fields.CharField(max_length=20, null=True, description="文件后缀名", index=True)
    size_bytes = fields.BigIntField(null=True, description="文件大小 (字节)")
    content_hash = fields.CharField(max_length=128, null=True, description="文件内容哈希", index=True)
    hash_algorithm = fields.CharEnumField(HashAlgorithm, max_length=10, null=True, description="哈希算法")
    page_count = fields.IntField(null=True, description="页数 (例如PDF)")

    # --- Storage Specific Attributes ---
    bucket_name = fields.CharField(max_length=63, description="MinIO Bucket名称")
    object_key = fields.CharField(max_length=1024, unique=True, null=True, description="MinIO对象存储路径/Key (仅文件)", index=True)

    # --- Status and Custom Data ---
    status_processing = fields.CharEnumField(
        ProcessingStatus,
        max_length=20,
        null=True,
        description="后台处理状态 (页面拆分、预览生成等)",
        index=True
    )
    custom_metadata = fields.JSONField(null=True, description="用户自定义元数据 (JSON)")

    # --- Soft Delete ---
    deleted_at = fields.DatetimeField(null=True, index=True, description="软删除时间标记 (NULL表示未删除)")

    # --- Relationships defined by related_name ---
    children: fields.ReverseRelation["File"]

    class Meta:
        table = "file" # Changed from "file_metadata" to "file"
        ordering = ["-created_at"]
        unique_together = (("user", "parent", "name", "deleted_at"),)

    # 3. Keep the get_full_path for now
    async def get_full_path(self) -> str:
        """
        Recursively builds the full path string for this file/directory.
        Example: /folder_a/subfolder_b/file.txt
        Be mindful of N+1 query issues if used in loops for many objects.
        """
        path_parts = [self.name]
        current_parent = await self.parent
        while current_parent:
            path_parts.insert(0, current_parent.name)
            current_parent = await current_parent.parent
        return "/" + "/".join(path_parts)

    def __str__(self):
        prefix = "D" if self.is_directory else "F"
        status = f" (Deleted)" if self.deleted_at else ""
        parent_id_val = self.parent_id # Access the FK field directly
        user_id_val = self.user_id
        return (f"{prefix}: {self.name} (ID: {self.id}, ParentID: {parent_id_val}, UserID: {user_id_val}){status}")