from datetime import datetime
from typing import List, <PERSON><PERSON>, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from tortoise.expressions import Q
from tortoise.transactions import atomic

from app.core.crud import CRUDBase, Total
from app.log import logger
from app.models.bundle import Bundle
from app.models.file import File
from app.schemas.bundles import BundleCreate, BundleUpdate, BundleRead
from app.schemas.files import FileRead
from app.schemas.pages import PageRead


class BundleController(CRUDBase[Bundle, BundleCreate, BundleUpdate]):
    """
    Controller for Bundle operations.
    Handles CRUD operations for Bundle entities and their relationships with Files.
    """

    def __init__(self):
        super().__init__(model=Bundle)

    async def create_bundle(self, bundle_in: BundleCreate) -> BundleRead:
        """
        Create a new Bundle and associate it with files if provided.

        Args:
            bundle_in: Bundle creation data with optional file_ids

        Returns:
            BundleRead: Created bundle data
        """
        try:
            # Extract file_ids before creating the bundle
            file_ids = bundle_in.file_ids or []

            # Create bundle without file associations first
            bundle_dict = bundle_in.model_dump(exclude={"file_ids"})
            bundle = await self.create(bundle_dict)

            # Add file associations if any
            if file_ids:
                await self._add_files_to_bundle(bundle, file_ids)

            # Return the created bundle
            return await self.get_bundle_info(bundle.id)

        except Exception as e:
            logger.error(f"Error creating bundle: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to create bundle: {str(e)}")

    async def update_bundle(self, bundle_id: int, bundle_update: BundleUpdate) -> BundleRead:
        """
        Update a Bundle and its file associations.

        Args:
            bundle_id: ID of the bundle to update
            bundle_update: Update data with optional file_ids

        Returns:
            BundleRead: Updated bundle data
        """
        try:
            # Get the bundle
            bundle = await self.model.get_or_none(id=bundle_id, deleted_at__isnull=True)
            if not bundle:
                raise HTTPException(status_code=404, detail="Bundle not found")

            # Extract file_ids if provided
            file_ids = bundle_update.file_ids

            # Update bundle data
            update_dict = bundle_update.model_dump(exclude={"file_ids"}, exclude_unset=True, exclude_none=True)
            if update_dict:  # Only update if there are fields to update
                bundle = await self.update(id=bundle_id, obj_in=update_dict)

            # Update file associations if provided
            if file_ids is not None:  # Only update files if explicitly provided
                await self._update_bundle_files(bundle, file_ids)

            # Return the updated bundle
            return await self.get_bundle_info(bundle_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating bundle: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to update bundle: {str(e)}")

    async def list_bundles(
        self,
        page: int = 1,
        page_size: int = 20,
        name: Optional[str] = None,
        bundle_type: Optional[str] = None,
        user_id: Optional[int] = None,
        include_deleted: bool = False
    ) -> Tuple[Total, List[BundleRead]]:
        """
        List bundles with filtering options.

        Args:
            page: Page number
            page_size: Items per page
            name: Filter by name (contains)
            bundle_type: Filter by bundle type
            user_id: Filter by user ID
            include_deleted: Whether to include soft-deleted bundles

        Returns:
            Tuple[Total, List[BundleRead]]: Total count and list of bundles
        """
        try:
            # Build query
            query = Q()

            # Filter by deletion status
            if not include_deleted:
                query &= Q(deleted_at__isnull=True)

            # Apply other filters
            if name:
                query &= Q(name__contains=name)
            if bundle_type:
                query &= Q(bundle_type=bundle_type)
            if user_id:
                query &= Q(user_id=user_id)

            # Execute query
            total, bundles = await self.list(
                page=page,
                page_size=page_size,
                search=query,
                order=["-created_at"]  # Newest first
            )

            # Convert to BundleRead objects
            bundle_reads = []
            for bundle in bundles:
                bundle_read = BundleRead.model_validate(bundle)
                bundle_reads.append(bundle_read)

            return total, bundle_reads

        except Exception as e:
            logger.error(f"Error listing bundles: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to list bundles: {str(e)}")

    async def get_bundle_info(self, bundle_id: int) -> BundleRead:
        """
        Get detailed information about a bundle.

        Args:
            bundle_id: Bundle ID

        Returns:
            BundleRead: Bundle data
        """
        try:
            # Get the bundle
            bundle = await self.model.get_or_none(id=bundle_id)
            if not bundle:
                raise HTTPException(status_code=404, detail="Bundle not found")

            # Convert to BundleRead
            bundle_read = BundleRead.model_validate(bundle)
            return bundle_read

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting bundle: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get bundle: {str(e)}")

    async def soft_delete(self, bundle_id: int) -> BundleRead:
        """
        Soft delete a bundle by setting its deleted_at field.

        Args:
            bundle_id: Bundle ID

        Returns:
            BundleRead: Deleted bundle data
        """
        try:
            # Get the bundle
            bundle = await self.model.get_or_none(id=bundle_id, deleted_at__isnull=True)
            if not bundle:
                raise HTTPException(status_code=404, detail="Bundle not found or already deleted")

            # Set deleted_at timestamp
            bundle.deleted_at = datetime.now()
            await bundle.save()

            # Return the deleted bundle
            return BundleRead.model_validate(bundle)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error soft deleting bundle: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to delete bundle: {str(e)}")

    async def restore(self, bundle_id: int) -> BundleRead:
        """
        Restore a soft-deleted bundle.

        Args:
            bundle_id: Bundle ID

        Returns:
            BundleRead: Restored bundle data
        """
        try:
            # Get the bundle
            bundle = await self.model.get_or_none(id=bundle_id)
            if not bundle:
                raise HTTPException(status_code=404, detail="Bundle not found")

            if bundle.deleted_at is None:
                raise HTTPException(status_code=400, detail="Bundle is not deleted")

            # Clear deleted_at timestamp
            bundle.deleted_at = None
            await bundle.save()

            # Return the restored bundle
            return BundleRead.model_validate(bundle)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error restoring bundle: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to restore bundle: {str(e)}")

    async def get_bundle_files(self, bundle_id: int) -> List[FileRead]:
        """
        Get all files associated with a bundle.

        Args:
            bundle_id: Bundle ID

        Returns:
            List[FileRead]: List of file data
        """
        try:
            # Get the bundle
            bundle = await self.model.get_or_none(id=bundle_id)
            if not bundle:
                raise HTTPException(status_code=404, detail="Bundle not found")

            # Get associated files
            await bundle.fetch_related("files")
            files = await bundle.files.all()

            # Convert to FileRead objects
            file_reads = []
            for file in files:
                file_read = FileRead.model_validate(file)
                file_reads.append(file_read)

            return file_reads

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting bundle files: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get bundle files: {str(e)}")

    @atomic()
    async def _add_files_to_bundle(self, bundle: Bundle, file_ids: List[int]) -> None:
        """
        Add files to a bundle.

        Args:
            bundle: Bundle object
            file_ids: List of file IDs to add
        """
        for file_id in file_ids:
            file = await File.get_or_none(id=file_id)
            if file:
                await bundle.files.add(file)

    @atomic()
    async def _update_bundle_files(self, bundle: Bundle, file_ids: List[int]) -> None:
        """
        Update the files associated with a bundle.

        Args:
            bundle: Bundle object
            file_ids: New list of file IDs
        """
        # Clear existing associations
        await bundle.files.clear()

        # Add new associations
        await self._add_files_to_bundle(bundle, file_ids)

    async def get_pages(self, bundle_id: int) -> List[PageRead]:
        """
        Get all pages associated with a bundle.

        Args:
            bundle_id: Bundle ID

        Returns:
            List[PageRead]: List of pages
        """
        try:
            # Verify that the bundle exists
            bundle = await self.model.get_or_none(id=bundle_id)
            if not bundle:
                raise HTTPException(status_code=404, detail=f"Bundle with ID {bundle_id} not found")

            # Get pages for the bundle
            await bundle.fetch_related("pages")
            pages = await bundle.pages.all().order_by("page_number")

            # Convert to PageRead objects
            page_reads = []
            for page in pages:
                page_read = PageRead.model_validate(page)
                page_reads.append(page_read)

            return page_reads

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting bundle pages: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get bundle pages: {str(e)}")

    async def delete_pages(self, bundle_id: int) -> int:
        """
        Delete all pages associated with a bundle using bulk delete for better performance.

        Args:
            bundle_id: Bundle ID

        Returns:
            int: Number of pages deleted
        """
        try:
            # Verify that the bundle exists
            bundle = await self.model.get_or_none(id=bundle_id)
            if not bundle:
                raise HTTPException(status_code=404, detail=f"Bundle with ID {bundle_id} not found")

            # Import Page model here to avoid circular imports
            from app.models.page import Page

            # Use bulk delete for better performance
            deleted_count = await Page.filter(bundle_id=bundle_id).delete()

            logger.info(f"Deleted {deleted_count} pages for bundle {bundle_id}")
            return deleted_count

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting bundle pages: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to delete bundle pages: {str(e)}")


# Instantiate the controller for use in the application
bundle_controller = BundleController()
