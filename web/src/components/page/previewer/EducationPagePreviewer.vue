<template>
  <div class="education-page-previewer">
    <NTabs type="line" animated v-model:value="activeTab" @update:value="handleTabChange">
      <!-- Image with CV Regions Tab -->
      <NTabPane name="image" tab="图像分析">
        <div class="image-container">
          <div v-if="page.image_url" class="image-wrapper">
            <canvas
              ref="canvasRef"
              class="page-canvas"
            />

            <div class="image-info">
              <NTag size="small">
                {{ page.image_width }}×{{ page.image_height }}
              </NTag>
              <NTag size="small" v-if="page.image_ppi">
                {{ page.image_ppi }} PPI
              </NTag>
              <NTag size="small" v-if="cvRegions.length">
                {{ cvRegions.length }} 个区域
              </NTag>
            </div>
          </div>
          <div v-else class="no-image">
            <TheIcon icon="material-symbols:image-not-supported-outline" :size="48" />
            <p>暂无图像</p>
          </div>
        </div>
      </NTabPane>

      <!-- Parsed Questions Tab -->
      <NTabPane name="questions" tab="试题内容">
        <div class="questions-container">
          <!-- Debug info -->
          <div v-if="!questions.length" style="margin-bottom: 16px; padding: 12px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">
            <div><strong>Debug Info:</strong></div>
            <div>Questions length: {{ questions.length }}</div>
            <div>OCR result exists: {{ !!page.ocr_result }}</div>
            <div>OCR result type: {{ typeof page.ocr_result }}</div>
            <div v-if="page.ocr_result">OCR result preview: {{ JSON.stringify(page.ocr_result).substring(0, 200) }}...</div>
          </div>

          <div v-if="questions.length" class="questions-wrapper">
            <div
              v-for="(question, index) in questions"
              :key="index"
              class="question-item"
            >
              <NCard :title="`题目 ${index + 1}`" size="small">
                <div class="question-content">
                  <!-- Question text with LaTeX support -->
                  <div class="question-text">
                    <MathJax
                      v-if="question.processedContent || question.content || question.stem"
                      :formula="String(question.processedContent || question.content || question.stem || '')"
                    />
                    <div v-else class="no-content">暂无题目内容</div>
                  </div>

                  <!-- Question metadata -->
                  <div v-if="question.type || question.question_type" class="question-meta">
                    <NTag size="small" type="info">{{ question.type || question.question_type }}</NTag>
                    <NTag v-if="question.difficulty" size="small" type="warning">
                      难度: {{ question.difficulty }}
                    </NTag>
                  </div>

                  <!-- Options for multiple choice questions - Array format -->
                  <div v-if="question.options && Array.isArray(question.options) && question.options.length" class="question-options">
                    <div
                      v-for="(option, optIndex) in question.options"
                      :key="optIndex"
                      class="option-item"
                    >
                      <span class="option-label">{{ String.fromCharCode(65 + optIndex) }}.</span>
                      <MathJax :formula="String(option.processedContent || option.content || option || '')" />
                    </div>
                  </div>

                  <!-- Options for multiple choice questions - Object format -->
                  <div v-if="question.processedOptions && question.processedOptions.length" class="question-options">
                    <div
                      v-for="option in question.processedOptions"
                      :key="option.key"
                      class="option-item"
                    >
                      <span class="option-label">{{ option.key }}.</span>
                      <MathJax :formula="String(option.processedContent || '')" />
                    </div>
                  </div>

                  <!-- Answer if available -->
                  <div v-if="question.answer" class="question-answer">
                    <strong>答案:</strong>
                    <MathJax :formula="String(question.processedAnswer || question.answer || '')" />
                  </div>
                </div>
              </NCard>
            </div>
          </div>
          <div v-else class="no-questions">
            <TheIcon icon="material-symbols:quiz-outline" :size="48" />
            <p>暂无试题内容</p>
          </div>
        </div>
      </NTabPane>

      <!-- Raw Data Tab -->
      <NTabPane name="raw" tab="原始数据">
        <div class="raw-data-container">
          <NSpace vertical>
            <NCard title="OCR结果" size="small">
              <JsonEditor
                :model-value="page.ocr_result"
                :read-only="true"
                mode="tree"
                height="300px"
              />
            </NCard>
            <NCard title="CV结果" size="small">
              <JsonEditor
                :model-value="page.cv_result"
                :read-only="true"
                mode="tree"
                height="300px"
              />
            </NCard>
            <NCard title="解析结果" size="small">
              <JsonEditor
                :model-value="page.process_result"
                :read-only="true"
                mode="tree"
                height="300px"
              />
            </NCard>
          </NSpace>
        </div>
      </NTabPane>
    </NTabs>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { NTabs, NTabPane, NCard, NTag, NSpace, useMessage } from 'naive-ui'
import { Canvas, FabricImage, Rect, Text } from 'fabric'
import { MathJax } from 'mathjax-vue3'
import TheIcon from '@/components/icon/TheIcon.vue'
import JsonEditor from '@/components/common/JsonEditor.vue'
import fileApi from '@/api/file'

defineOptions({ name: 'EducationPagePreviewer' })

const props = defineProps({
  page: {
    type: Object,
    required: true
  },
  editMode: {
    type: Boolean,
    default: false
  }
})

const message = useMessage()

// Component state
const canvasRef = ref(null)
const fabricCanvas = ref(null)
const activeTab = ref('image')

// Computed properties
const cvRegions = computed(() => {
  if (!props.page.cv_result) return []

  try {
    // Handle empty or invalid JSON strings
    if (typeof props.page.cv_result === 'string') {
      const trimmed = props.page.cv_result.trim()
      if (!trimmed || trimmed === '{}' || trimmed === '[]') {
        return []
      }

      // Convert Python dict format to JSON format (single quotes to double quotes)
      const jsonString = trimmed.replace(/'/g, '"')
      const cvData = JSON.parse(jsonString)

      // Extract regions from different possible structures
      return cvData.objects || cvData.regions || cvData.boxes || []
    } else {
      const cvData = props.page.cv_result
      return cvData.objects || cvData.regions || cvData.boxes || []
    }
  } catch (error) {
    console.error('Error parsing CV result:', error, 'Raw data:', props.page.cv_result)
    return []
  }
})

const questions = computed(() => {
  if (!props.page.ocr_result) {
    return []
  }

  try {
    const ocrData = typeof props.page.ocr_result === 'string'
      ? JSON.parse(props.page.ocr_result)
      : props.page.ocr_result

    // Handle different possible structures
    if (Array.isArray(ocrData)) {
      return ocrData.map(processQuestion)
    } else if (ocrData.questions) {
      return ocrData.questions.map(processQuestion)
    } else if (ocrData.items) {
      return ocrData.items.map(processQuestion)
    }

    return []
  } catch (error) {
    console.error('Error parsing ocr result:', error, 'Raw data:', props.page.ocr_result)
    return []
  }
})

// Helper functions
function processQuestion(question) {
  // Process LaTeX content for better rendering
  const processedQuestion = { ...question }

  // Handle both 'content' and 'stem' fields for question text
  const questionText = question.content || question.stem
  if (questionText) {
    processedQuestion.processedContent = processLatexContent(questionText)
  }

  if (question.answer) {
    processedQuestion.processedAnswer = processLatexContent(question.answer)
  }

  // Handle options - can be object or array
  if (question.options) {
    if (Array.isArray(question.options)) {
      // Array format: [{content: "..."}, ...]
      processedQuestion.options = question.options.map(option => ({
        ...option,
        processedContent: processLatexContent(option.content || option)
      }))
    } else if (typeof question.options === 'object') {
      // Object format: {A: "...", B: "...", ...}
      processedQuestion.processedOptions = Object.entries(question.options).map(([key, value]) => ({
        key,
        content: value,
        processedContent: processLatexContent(value)
      }))
    }
  }

  return processedQuestion
}

function processLatexContent(content) {
  if (!content) return ''

  // Convert common LaTeX patterns to MathJax format
  let processed = content
    .replace(/\$\$(.*?)\$\$/g, '$$$$1$$') // Block math
    .replace(/\$(.*?)\$/g, '$$$1$$') // Inline math
    .replace(/\\begin\{equation\}(.*?)\\end\{equation\}/g, '$$$$1$$') // Equation blocks

  return processed
}



// Canvas functions
async function initializeCanvas() {
  if (!canvasRef.value || !props.page.image_url) {
    return
  }

  try {
    // Dispose existing canvas if it exists
    if (fabricCanvas.value) {
      fabricCanvas.value.dispose()
      fabricCanvas.value = null
    }

    // Initialize Fabric.js canvas
    fabricCanvas.value = new Canvas(canvasRef.value, {
      selection: false,
      interactive: false
    })

    // Load and display the image
    await loadImage()

    // Draw CV regions
    drawCVRegions()
  } catch (error) {
    console.error('Error initializing canvas:', error)
    message.error('初始化画布失败')
  }
}

async function loadImage() {
  try {
    // Get download URL for the page image
    const response = await fileApi.getDownloadUrlByPageId(props.page.id)

    if (!response || !response.data) {
      throw new Error('Failed to get download URL')
    }

    const imageUrl = response.data

    return new Promise((resolve, reject) => {
      FabricImage.fromURL(imageUrl, {
        crossOrigin: 'anonymous'
      }).then((img) => {
        if (!img) {
          reject(new Error('Failed to load image'))
          return
        }

        // Calculate scale to fit canvas
        const maxWidth = 800
        const maxHeight = 600
        const scale = Math.min(
          maxWidth / img.width,
          maxHeight / img.height,
          1
        )

        img.scale(scale)

        // Set canvas size
        fabricCanvas.value.setDimensions({
          width: img.width * scale,
          height: img.height * scale
        })

        // Set image as background - correct way according to Fabric.js docs
        fabricCanvas.value.backgroundImage = img
        fabricCanvas.value.renderAll()

        resolve()
      }).catch((error) => {
        console.error('FabricImage.fromURL error:', error)
        reject(error)
      })
    })
  } catch (error) {
    console.error('Error getting download URL:', error)
    throw error
  }
}

function drawCVRegions() {
  if (!fabricCanvas.value || !cvRegions.value.length) return

  // Get canvas scale factor
  const canvasWidth = fabricCanvas.value.width
  const canvasHeight = fabricCanvas.value.height
  const imageWidth = props.page.image_width || canvasWidth
  const imageHeight = props.page.image_height || canvasHeight

  const scaleX = canvasWidth / imageWidth
  const scaleY = canvasHeight / imageHeight

  cvRegions.value.forEach((region, index) => {
    // Handle different region formats
    let x, y, width, height

    if (region.position) {
      // CV result format: {position: {x, y, w, h}}
      x = region.position.x * scaleX
      y = region.position.y * scaleY
      width = region.position.w * scaleX
      height = region.position.h * scaleY
    } else if (region.bbox) {
      // Bbox format: [x, y, w, h]
      x = region.bbox[0] * scaleX
      y = region.bbox[1] * scaleY
      width = region.bbox[2] * scaleX
      height = region.bbox[3] * scaleY
    } else {
      // Direct format: {x, y, width, height} or {left, top, width, height}
      x = (region.x || region.left || 0) * scaleX
      y = (region.y || region.top || 0) * scaleY
      width = (region.width || region.w || 0) * scaleX
      height = (region.height || region.h || 0) * scaleY
    }

    // Create rectangle
    const rect = new Rect({
      left: x,
      top: y,
      width: width,
      height: height,
      fill: 'transparent',
      stroke: '#ff4757',
      strokeWidth: 2,
      selectable: false,
      evented: false
    })

    fabricCanvas.value.add(rect)

    // Add label
    const label = new Text(`${index + 1}`, {
      left: x + 5,
      top: y + 5,
      fontSize: 14,
      fill: '#ff4757',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      selectable: false,
      evented: false
    })

    fabricCanvas.value.add(label)
  })

  // Force canvas to render after adding regions
  fabricCanvas.value.renderAll()
}

// Handle tab change
async function handleTabChange(tabName) {
  // Don't update activeTab here to avoid infinite loop
  // activeTab.value = tabName

  // If switching to image tab, ensure canvas is properly rendered
  if (tabName === 'image') {
    await nextTick()

    // Check if canvas exists and has content
    if (fabricCanvas.value) {
      // Try to render first
      fabricCanvas.value.renderAll()

      // If background image is missing, reinitialize
      setTimeout(async () => {
        if (fabricCanvas.value && !fabricCanvas.value.backgroundImage && props.page.image_url) {
          await initializeCanvas()
        }
      }, 100)
    } else if (props.page.image_url) {
      // If canvas doesn't exist but should, initialize it
      setTimeout(async () => {
        await initializeCanvas()
      }, 100)
    }
  }
}

// Watch for page changes
watch(() => props.page, async (newPage, oldPage) => {
  if (newPage && newPage.id !== oldPage?.id) {
    await nextTick()
    await initializeCanvas()
  }
}, { immediate: false })

// Lifecycle
onMounted(async () => {
  await nextTick()
  await initializeCanvas()
})

onUnmounted(() => {
  if (fabricCanvas.value) {
    fabricCanvas.value.dispose()
    fabricCanvas.value = null
  }
})
</script>

<style scoped>
.education-page-previewer {
  height: 100%;
}

.image-container {
  padding: 16px 0;
  text-align: center;
}

.image-wrapper {
  display: inline-block;
}

.page-canvas {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.no-image,
.no-questions {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.no-image p,
.no-questions p {
  margin: 16px 0 0 0;
}

.questions-container {
  padding: 16px 0;
}

.questions-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
}

.question-meta {
  display: flex;
  gap: 8px;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 16px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.option-label {
  font-weight: bold;
  min-width: 20px;
}

.question-answer {
  padding: 12px;
  background: #f0f8ff;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.no-content {
  color: #999;
  font-style: italic;
}

.raw-data-container {
  padding: 16px 0;
}


</style>
