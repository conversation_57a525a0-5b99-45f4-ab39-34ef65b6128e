<template>
  <div class="page-previewer">
    <NTabs type="line" animated>
      <!-- Image Tab -->
      <NTabPane name="image" tab="原始图像">
        <div class="image-container">
          <div v-if="page.image_url" class="image-wrapper">
            <img
              :src="imageUrl"
              :alt="`第${page.page_number}页`"
              class="page-image"
              @load="handleImageLoad"
              @error="handleImageError"
            />
            <div class="image-info">
              <NTag size="small">
                {{ page.image_width }}×{{ page.image_height }}
              </NTag>
              <NTag size="small" v-if="page.image_ppi">
                {{ page.image_ppi }} PPI
              </NTag>
            </div>
          </div>
          <div v-else class="no-image">
            <TheIcon icon="material-symbols:image-not-supported-outline" :size="48" />
            <p>暂无图像</p>
          </div>
        </div>
      </NTabPane>

      <!-- Content Tab -->
      <NTabPane name="content" tab="解析内容">
        <div class="content-container">
          <div v-if="processResult" class="content-wrapper">
            <div class="content-item" v-for="(item, index) in processResult" :key="index">
              <NCard :title="`内容块 ${index + 1}`" size="small">
                <pre class="content-text">{{ JSON.stringify(item, null, 2) }}</pre>
              </NCard>
            </div>
          </div>
          <div v-else class="no-content">
            <TheIcon icon="material-symbols:description-outline" :size="48" />
            <p>暂无解析内容</p>
          </div>
        </div>
      </NTabPane>

      <!-- OCR Results Tab -->
      <NTabPane name="ocr" tab="OCR结果">
        <div class="ocr-container">
          <div v-if="ocrResult" class="ocr-wrapper">
            <NCard title="OCR识别结果" size="small">
              <pre class="ocr-text">{{ JSON.stringify(ocrResult, null, 2) }}</pre>
            </NCard>
          </div>
          <div v-else class="no-ocr">
            <TheIcon icon="material-symbols:text-fields" :size="48" />
            <p>暂无OCR结果</p>
          </div>
        </div>
      </NTabPane>

      <!-- CV Results Tab -->
      <NTabPane name="cv" tab="CV结果">
        <div class="cv-container">
          <div v-if="cvResult" class="cv-wrapper">
            <NCard title="计算机视觉分析结果" size="small">
              <pre class="cv-text">{{ JSON.stringify(cvResult, null, 2) }}</pre>
            </NCard>
          </div>
          <div v-else class="no-cv">
            <TheIcon icon="material-symbols:computer-vision" :size="48" />
            <p>暂无CV结果</p>
          </div>
        </div>
      </NTabPane>
    </NTabs>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { NTabs, NTabPane, NCard, NTag, useMessage } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'
import fileApi from '@/api/file'

defineOptions({ name: 'PagePreviewer' })

const props = defineProps({
  page: {
    type: Object,
    required: true
  },
  editMode: {
    type: Boolean,
    default: false
  }
})

const message = useMessage()
const imageUrl = ref('')

// Fetch download URL for the page image
async function fetchImageUrl() {
  if (!props.page.id || !props.page.image_url) return

  try {
    const response = await fileApi.getDownloadUrlByPageId(props.page.id)
    if (response && response.data) {
      imageUrl.value = response.data
    }
  } catch (error) {
    console.error('Error fetching image URL:', error)
    message.error('获取图像链接失败')
  }
}

// Watch for page changes
watch(() => props.page, async (newPage, oldPage) => {
  if (newPage && newPage.id !== oldPage?.id) {
    console.log('Page changed in previewer, fetching new image URL:', newPage.id)
    await fetchImageUrl()
  }
}, { immediate: true })

// Fetch image URL on mount
onMounted(() => {
  fetchImageUrl()
})

// Computed properties for parsed data
const processResult = computed(() => {
  if (!props.page.process_result) return null

  try {
    return typeof props.page.process_result === 'string'
      ? JSON.parse(props.page.process_result)
      : props.page.process_result
  } catch (error) {
    console.error('Error parsing process_result:', error)
    return null
  }
})

const ocrResult = computed(() => {
  if (!props.page.ocr_result) return null

  try {
    return typeof props.page.ocr_result === 'string'
      ? JSON.parse(props.page.ocr_result)
      : props.page.ocr_result
  } catch (error) {
    console.error('Error parsing ocr_result:', error)
    return null
  }
})

const cvResult = computed(() => {
  if (!props.page.cv_result) return null

  try {
    return typeof props.page.cv_result === 'string'
      ? JSON.parse(props.page.cv_result)
      : props.page.cv_result
  } catch (error) {
    console.error('Error parsing cv_result:', error)
    return null
  }
})

// Event handlers
function handleImageLoad(event) {
  console.log('Image loaded:', event.target.naturalWidth, 'x', event.target.naturalHeight)
}

function handleImageError(event) {
  console.error('Image load error:', event)
  message.error('图像加载失败')
}
</script>

<style scoped>
.page-previewer {
  height: 100%;
}

.image-container,
.content-container,
.ocr-container,
.cv-container {
  padding: 16px 0;
}

.image-wrapper {
  position: relative;
  text-align: center;
}

.page-image {
  max-width: 100%;
  max-height: 70vh;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.no-image,
.no-content,
.no-ocr,
.no-cv {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.no-image p,
.no-content p,
.no-ocr p,
.no-cv p {
  margin: 16px 0 0 0;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.content-text,
.ocr-text,
.cv-text {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
