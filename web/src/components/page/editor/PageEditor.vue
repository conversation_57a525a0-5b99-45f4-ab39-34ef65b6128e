<template>
  <div class="page-editor">
    <div class="editor-toolbar">
      <NSpace>
        <NButton type="primary" @click="handleSave" :loading="saving">
          <template #icon>
            <TheIcon icon="material-symbols:save" />
          </template>
          保存
        </NButton>
        <NButton @click="handleReset">
          <template #icon>
            <TheIcon icon="material-symbols:refresh" />
          </template>
          重置
        </NButton>
      </NSpace>
    </div>

    <NTabs type="line" animated>
      <!-- Image Tab -->
      <NTabPane name="image" tab="原始图像">
        <div class="image-container">
          <div v-if="page.image_url" class="image-wrapper">
            <img
              :src="imageUrl"
              :alt="`第${page.page_number}页`"
              class="page-image"
            />
            <div class="image-info">
              <NTag size="small">
                {{ page.image_width }}×{{ page.image_height }}
              </NTag>
              <NTag size="small" v-if="page.image_ppi">
                {{ page.image_ppi }} PPI
              </NTag>
            </div>
          </div>
          <div v-else class="no-image">
            <TheIcon icon="material-symbols:image-not-supported-outline" :size="48" />
            <p>暂无图像</p>
          </div>
        </div>
      </NTabPane>

      <!-- Content Editor Tab -->
      <NTabPane name="content" tab="解析内容">
        <div class="content-editor">
          <NCard title="解析结果编辑" size="small">
            <NInput
              v-model:value="editableProcessResult"
              type="textarea"
              placeholder="请输入解析结果 (JSON格式)"
              :rows="20"
              :autosize="{ minRows: 10, maxRows: 30 }"
            />
          </NCard>
        </div>
      </NTabPane>

      <!-- OCR Editor Tab -->
      <NTabPane name="ocr" tab="OCR结果">
        <div class="ocr-editor">
          <NCard title="OCR结果编辑" size="small">
            <NInput
              v-model:value="editableOcrResult"
              type="textarea"
              placeholder="请输入OCR结果 (JSON格式)"
              :rows="20"
              :autosize="{ minRows: 10, maxRows: 30 }"
            />
          </NCard>
        </div>
      </NTabPane>

      <!-- CV Editor Tab -->
      <NTabPane name="cv" tab="CV结果">
        <div class="cv-editor">
          <NCard title="CV结果编辑" size="small">
            <NInput
              v-model:value="editableCvResult"
              type="textarea"
              placeholder="请输入CV结果 (JSON格式)"
              :rows="20"
              :autosize="{ minRows: 10, maxRows: 30 }"
            />
          </NCard>
        </div>
      </NTabPane>
    </NTabs>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { NTabs, NTabPane, NCard, NTag, NButton, NSpace, NInput, useMessage } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'
import api from '@/api'
import fileApi from '@/api/file'

defineOptions({ name: 'PageEditor' })

const props = defineProps({
  page: {
    type: Object,
    required: true
  },
  editMode: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['save'])
const message = useMessage()

// Component state
const saving = ref(false)
const imageUrl = ref('')
const editableProcessResult = ref('')
const editableOcrResult = ref('')
const editableCvResult = ref('')

// Fetch download URL for the page image
async function fetchImageUrl() {
  if (!props.page.id || !props.page.image_url) return

  try {
    const response = await fileApi.getDownloadUrlByPageId(props.page.id)
    if (response && response.data) {
      imageUrl.value = response.data
    }
  } catch (error) {
    console.error('Error fetching image URL:', error)
    message.error('获取图像链接失败')
  }
}

// Initialize editable data
function initializeEditableData() {
  editableProcessResult.value = formatJsonString(props.page.process_result)
  editableOcrResult.value = formatJsonString(props.page.ocr_result)
  editableCvResult.value = formatJsonString(props.page.cv_result)
}

// Helper function to format JSON string
function formatJsonString(data) {
  if (!data) return ''

  try {
    const parsed = typeof data === 'string' ? JSON.parse(data) : data
    return JSON.stringify(parsed, null, 2)
  } catch (error) {
    return typeof data === 'string' ? data : JSON.stringify(data, null, 2)
  }
}

// Validate JSON string
function validateJson(jsonString) {
  if (!jsonString.trim()) return { valid: true, data: null }

  try {
    const parsed = JSON.parse(jsonString)
    return { valid: true, data: parsed }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

// Event handlers
async function handleSave() {
  try {
    saving.value = true

    // Validate all JSON fields
    const processValidation = validateJson(editableProcessResult.value)
    const ocrValidation = validateJson(editableOcrResult.value)
    const cvValidation = validateJson(editableCvResult.value)

    if (!processValidation.valid) {
      message.error(`解析结果格式错误: ${processValidation.error}`)
      return
    }

    if (!ocrValidation.valid) {
      message.error(`OCR结果格式错误: ${ocrValidation.error}`)
      return
    }

    if (!cvValidation.valid) {
      message.error(`CV结果格式错误: ${cvValidation.error}`)
      return
    }

    // Prepare update data
    const updateData = {
      id: props.page.id,
      process_result: processValidation.data,
      ocr_result: ocrValidation.data,
      cv_result: cvValidation.data
    }

    // Call API to update page
    const response = await api.updatePage(updateData)

    if (response.code === 200) {
      message.success('保存成功')
      emit('save', updateData)
    } else {
      message.error(`保存失败: ${response.msg}`)
    }
  } catch (error) {
    console.error('Error saving page:', error)
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

function handleReset() {
  initializeEditableData()
  message.info('已重置为原始数据')
}

// Watch for page changes
watch(() => props.page, async (newPage, oldPage) => {
  if (newPage && newPage.id !== oldPage?.id) {
    console.log('Page changed in editor, fetching new image URL:', newPage.id)
    initializeEditableData()
    await fetchImageUrl()
  }
}, { immediate: true })

// Fetch image URL on mount
onMounted(() => {
  fetchImageUrl()
})
</script>

<style scoped>
.page-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  padding: 16px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 16px;
}

.image-container {
  padding: 16px 0;
}

.image-wrapper {
  position: relative;
  text-align: center;
}

.page-image {
  max-width: 100%;
  max-height: 50vh;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.no-image {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.no-image p {
  margin: 16px 0 0 0;
}

.content-editor,
.ocr-editor,
.cv-editor {
  padding: 16px 0;
}
</style>
